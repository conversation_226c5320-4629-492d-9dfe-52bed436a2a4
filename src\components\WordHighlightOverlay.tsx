import React, { useEffect, useRef, useState } from 'react';
import { Box } from '@mui/material';
import type { TextItem } from '../types';

interface WordHighlightOverlayProps {
  textItems: TextItem[];
  pageNumber: number;
  scale: number;
  pageHeight: number;
  currentHighlightedWord?: TextItem | null;
  onWordClick?: (word: string, textItem: TextItem) => void;
  onSentenceClick?: (sentence: string, startIndex: number) => void;
  onParagraphClick?: (paragraph: string, startIndex: number) => void;
  onWordHover?: (word: string, textItem: TextItem) => void;
  onSentenceHover?: (sentence: string, startIndex: number) => void;
  onParagraphHover?: (paragraph: string, startIndex: number) => void;
  isReading?: boolean;
  speaking?: boolean;
  paused?: boolean;
  readWords?: Set<string>;
}

const WordHighlightOverlay: React.FC<WordHighlightOverlayProps> = ({
  textItems,
  pageNumber,
  scale,
  pageHeight,
  currentHighlightedWord,
  onWordClick,
  onSentenceClick,
  onParagraphClick,
  onWordHover,
  onSentenceHover,
  onParagraphHover,
  isReading = false,
  speaking = false,
  paused = false,
  readWords = new Set(),
}) => {
  const overlayRef = useRef<HTMLDivElement>(null);
  const [hoveredWord, setHoveredWord] = useState<TextItem | null>(null);
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null);
  const [lastHoveredItem, setLastHoveredItem] = useState<TextItem | null>(null);

  // Determine if words should be clickable (when audio is stopped or paused)
  const isClickable = !speaking || paused;
  const isStoppedAndReady = !speaking && !paused;
  const isHoverEnabled = !speaking; // Enable hover for both stopped and paused states

  // Filter text items for current page
  const pageTextItems = textItems.filter(item => item.pageIndex === pageNumber - 1);

  // Function to find sentence boundaries
  const findSentenceBoundaries = (clickedItem: TextItem): { start: number; end: number; text: string } => {
    const allText = pageTextItems.map(item => item.text).join(' ');
    const clickedIndex = clickedItem.startIndex;
    
    // Find sentence start (look backwards for sentence endings)
    let sentenceStart = 0;
    for (let i = clickedIndex - 1; i >= 0; i--) {
      const char = allText[i];
      if (char === '.' || char === '!' || char === '?') {
        sentenceStart = i + 1;
        break;
      }
    }
    
    // Find sentence end (look forwards for sentence endings)
    let sentenceEnd = allText.length;
    for (let i = clickedIndex; i < allText.length; i++) {
      const char = allText[i];
      if (char === '.' || char === '!' || char === '?') {
        sentenceEnd = i + 1;
        break;
      }
    }
    
    const sentenceText = allText.substring(sentenceStart, sentenceEnd).trim();
    return { start: sentenceStart, end: sentenceEnd, text: sentenceText };
  };

  // Function to find paragraph boundaries
  const findParagraphBoundaries = (clickedItem: TextItem): { start: number; end: number; text: string } => {
    const allText = pageTextItems.map(item => item.text).join(' ');
    const clickedIndex = clickedItem.startIndex;
    
    // Find paragraph start (look backwards for double line breaks or start of text)
    let paragraphStart = 0;
    for (let i = clickedIndex - 1; i >= 0; i--) {
      if (allText.substring(i, i + 2) === '\n\n' || allText.substring(i, i + 2) === '  ') {
        paragraphStart = i + 2;
        break;
      }
    }
    
    // Find paragraph end (look forwards for double line breaks or end of text)
    let paragraphEnd = allText.length;
    for (let i = clickedIndex; i < allText.length - 1; i++) {
      if (allText.substring(i, i + 2) === '\n\n' || allText.substring(i, i + 2) === '  ') {
        paragraphEnd = i;
        break;
      }
    }
    
    const paragraphText = allText.substring(paragraphStart, paragraphEnd).trim();
    return { start: paragraphStart, end: paragraphEnd, text: paragraphText };
  };

  const handleWordClick = (event: React.MouseEvent, textItem: TextItem) => {
    // Only allow clicks when audio is stopped or paused
    if (!isClickable) return;

    // Prevent event from bubbling up to parent components (like HTMLFlipBook)
    event.preventDefault();
    event.stopPropagation();
    (event.nativeEvent as any).stopImmediatePropagation?.();

    // Determine click type based on modifier keys
    if (event.ctrlKey || event.metaKey) {
      // Ctrl/Cmd + click for paragraph
      const paragraph = findParagraphBoundaries(textItem);
      onParagraphClick?.(paragraph.text, paragraph.start);
    } else if (event.shiftKey) {
      // Shift + click for sentence
      const sentence = findSentenceBoundaries(textItem);
      onSentenceClick?.(sentence.text, sentence.start);
    } else {
      // Regular click for word
      onWordClick?.(textItem.text, textItem);
    }
  };

  const handleWordHover = (textItem: TextItem | null) => {
    // Only allow hover effects when audio is not actively speaking
    if (!isHoverEnabled) return;

    setHoveredWord(textItem);

    // Clear any existing timeout
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
      setHoverTimeout(null);
    }

    // Start reading after a short delay when audio is stopped or paused
    if (textItem && textItem !== lastHoveredItem) {
      const delay = paused ? 600 : 800; // Shorter delay when paused for better responsiveness
      const timeout = setTimeout(() => {
        setLastHoveredItem(textItem);
        onWordHover?.(textItem.text, textItem);
      }, delay);

      setHoverTimeout(timeout);
    }
  };

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeout) {
        clearTimeout(hoverTimeout);
      }
    };
  }, [hoverTimeout]);

  const handleOverlayClick = (event: React.MouseEvent) => {
    // Prevent any clicks on the overlay from reaching the flip book
    event.preventDefault();
    event.stopPropagation();
    (event.nativeEvent as any).stopImmediatePropagation?.();
  };

  return (
    <>
      <style>
        {`
          @keyframes wordHighlight {
            0% {
              background: linear-gradient(135deg, rgba(255, 193, 7, 0.7) 0%, rgba(255, 235, 59, 0.8) 50%, rgba(255, 193, 7, 0.7) 100%);
              transform: scale(1.02);
              box-shadow: 0 2px 8px rgba(255, 193, 7, 0.4);
            }
            50% {
              background: linear-gradient(135deg, rgba(255, 193, 7, 1) 0%, rgba(255, 235, 59, 1) 50%, rgba(255, 193, 7, 1) 100%);
              transform: scale(1.05);
              box-shadow: 0 4px 16px rgba(255, 193, 7, 0.6);
            }
            100% {
              background: linear-gradient(135deg, rgba(255, 193, 7, 0.7) 0%, rgba(255, 235, 59, 0.8) 50%, rgba(255, 193, 7, 0.7) 100%);
              transform: scale(1.02);
              box-shadow: 0 2px 8px rgba(255, 193, 7, 0.4);
            }
          }

          @keyframes slideIn {
            0% {
              opacity: 0;
              transform: translateY(-5px) scale(0.95);
            }
            100% {
              opacity: 1;
              transform: translateY(0) scale(1);
            }
          }
        `}
      </style>
      <Box
        ref={overlayRef}
        onClick={handleOverlayClick}
        onMouseDown={handleOverlayClick}
        onMouseUp={handleOverlayClick}
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: isClickable ? 'auto' : 'none',
          zIndex: 1000,
        }}
      >
      {pageTextItems.map((textItem, index) => {
        const isCurrentWord = currentHighlightedWord && 
          textItem.startIndex === currentHighlightedWord.startIndex &&
          textItem.endIndex === currentHighlightedWord.endIndex;
        
        const isHovered = hoveredWord &&
          textItem.startIndex === hoveredWord.startIndex &&
          textItem.endIndex === hoveredWord.endIndex;

        const wordKey = `${textItem.pageIndex}-${textItem.startIndex}-${textItem.endIndex}`;
        const isReadWord = readWords.has(wordKey);
        const isClickable = !speaking || paused;

        return (
          <Box
            key={`word-${index}-${textItem.startIndex}`}
            sx={{
              position: 'absolute',
              left: `${textItem.coordinates.x * scale}px`,
              top: `${(pageHeight - textItem.coordinates.y - textItem.coordinates.height) * scale}px`,
              width: `${textItem.coordinates.width * scale}px`,
              height: `${textItem.coordinates.height * scale}px`,
              background: isCurrentWord
                ? 'linear-gradient(135deg, rgba(255, 193, 7, 0.9) 0%, rgba(255, 235, 59, 1) 50%, rgba(255, 193, 7, 0.9) 100%)'
                : isHovered && paused
                ? 'linear-gradient(135deg, rgba(255, 152, 0, 0.6) 0%, rgba(255, 193, 7, 0.7) 100%)'
                : isHovered && isStoppedAndReady
                ? 'linear-gradient(135deg, rgba(76, 175, 80, 0.5) 0%, rgba(129, 199, 132, 0.6) 100%)'
                : isHovered
                ? 'linear-gradient(135deg, rgba(33, 150, 243, 0.4) 0%, rgba(100, 181, 246, 0.5) 100%)'
                : isReadWord
                ? 'linear-gradient(135deg, rgba(76, 175, 80, 0.15) 0%, rgba(129, 199, 132, 0.2) 100%)'
                : paused
                ? 'rgba(255, 152, 0, 0.1)'
                : isStoppedAndReady
                ? 'rgba(255, 193, 7, 0.1)'
                : 'transparent',
              boxShadow: isCurrentWord
                ? '0 2px 12px rgba(255, 193, 7, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.4)'
                : isHovered
                ? '0 1px 6px rgba(0, 0, 0, 0.2)'
                : 'none',
              border: isCurrentWord
                ? '1px solid rgba(255, 152, 0, 0.8)'
                : isReadWord
                ? '1px solid rgba(76, 175, 80, 0.3)'
                : 'none',
              cursor: isClickable ? 'pointer' : 'default',
              borderRadius: '4px',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              animation: isCurrentWord ? 'wordHighlight 2s ease-in-out infinite' : 'none',
              transform: isCurrentWord ? 'scale(1.02)' : 'scale(1)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: `${12 * scale}px`,
              color: isCurrentWord ? 'rgba(0, 0, 0, 0.8)' : 'transparent',
              fontWeight: isCurrentWord ? 'bold' : 'normal',
              textShadow: isCurrentWord ? '1px 1px 2px rgba(255, 255, 255, 0.8)' : 'none',
              overflow: 'hidden',
              whiteSpace: 'nowrap',
            }}
            onClick={(e) => handleWordClick(e, textItem)}
            onMouseDown={(e) => {
              e.preventDefault();
              e.stopPropagation();
              (e.nativeEvent as any).stopImmediatePropagation?.();
            }}
            onMouseUp={(e) => {
              e.preventDefault();
              e.stopPropagation();
              (e.nativeEvent as any).stopImmediatePropagation?.();
            }}
            onMouseEnter={() => handleWordHover(textItem)}
            onMouseLeave={() => handleWordHover(null)}
            title={
              !isClickable
                ? 'Stop or pause audio to interact with words'
                : isStoppedAndReady
                ? `Hover to read from "${textItem.text}"\nClick: Read word\nShift+Click: Read sentence\nCtrl+Click: Read paragraph`
                : paused
                ? `Audio is paused - Hover or click to resume from "${textItem.text}"\nShift+Click: Read sentence\nCtrl+Click: Read paragraph`
                : `Hover or click to read from "${textItem.text}"\nShift+Click: Read sentence\nCtrl+Click: Read paragraph`
            }
          >
            {isCurrentWord && textItem.text}
          </Box>
        );
      })}
    </Box>
    </>
  );
};

export default WordHighlightOverlay;
